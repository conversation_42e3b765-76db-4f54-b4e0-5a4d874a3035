# ShortLink URL重定向服务

ShortLink是一个高性能的短链接重定向服务，基于Go语言开发。它提供URL格式保护加密(FPE)、缓存、速率限制和并发控制等特性。

## 类图

以下类图描述了系统的主要组件及其关系：

```mermaid
classDiagram
    class Config {
      +ServerConfig Server
      +LogConfig Log
      +RedirectConfig Redirect
      +CryptoConfig Crypto
      +CacheConfig Cache
      +RateConfig Rate
      +Load(path string) *Config
    }

    class URLParser {
      -string sourceDomain
      +Parse(path string) string
      +ValidateDomain(host string) error
      +ExtractPathFromURL(fullURL string) string
    }

    class FPECrypto {
      -int deviceIDLength
      -int linkIDLength
      +DecryptURL(plainURL string) string
      +DecryptDeviceID(encryptedID string) string
      +DecryptLinkID(encryptedID string) string
      +EncryptDeviceID(plainID string) string
      +EncryptLinkID(plainID string) string
    }

    class URLTransformer {
      -string baseURL
      -string pathTemplate
      +Transform(deviceID string, linkID string) string
    }

    class Cache {
      -map[string]Entry entries
      -sync.RWMutex mutex
      -time.Duration ttl
      -int maxSize
      -bool enabled
      +Get(key string) (string, bool)
      +Set(key string, value string)
      -cleanup()
    }

    class RedirectService {
      -URLParser urlParser
      -FPECrypto fpeDecryptor
      -URLTransformer urlTransformer
      -Cache cache
      -rate.Limiter limiter
      -chan semaphore
      -Stats stats
      -Config config
      +HandleRedirect(w http.ResponseWriter, r *http.Request)
      +ProcessURLWithTimeout(ctx context.Context, path string) string
      +ProcessURL(path string) string
      +GetStats() Stats
    }

    class Daemon {
      -Config cfg
      -http.Server server
      -RedirectService redirectService
      -chan shutdownCh
      +Start(ctx context.Context) error
      +Stop() error
    }

    class Stats {
      +int64 TotalRequests
      +int64 CacheHits
      +int64 CacheMisses
      +int64 ProcessingErrors
      +int64 RateLimitExceeded
      +time.Time LastRequestTime
      +time.Duration AverageProcessTime
      -time.Duration totalProcessTime
      -sync.RWMutex mutex
    }

    Config --> RedirectService
    URLParser --> RedirectService
    FPECrypto --> RedirectService
    URLTransformer --> RedirectService
    Cache --> RedirectService
    Stats --* RedirectService
    Config --> Daemon
    RedirectService --> Daemon
```

## 数据流程

1. 接收请求: `https://tnas.share.net/s/60f7098dc11f418d8f`
2. 解析URL: 提取 `60f7098dc11f418d8f`
3. 整体进行解密处理 `60f7098dc11f418d8f` → `6c56zu7tlyxatg000156`(最后两位作为校验位)
4. 设备ID重新加密:
   - 设备ID: `6c56zu7tlyxatg` → `5c46zu7tlyxatg8f`(加密多两位)
5. 构建目标URL: `https://tnas.online/e_5c46zu7tlyxatg8f/share/0001`
6. 执行307重定向

## 项目结构

```
shortlink/
├── cmd/
│   └── shortlinkd/            # 命令行入口点
│       └── main.go            # 主程序入口
├── internal/                  # 内部实现包
│   ├── cache/                 # 缓存实现
│   │   └── cache.go           # 缓存核心逻辑
│   ├── config/                # 配置处理
│   │   └── config.go          # 配置结构和加载
│   ├── crypto/                # 加密解密
│   │   ├── encryptor.go       # CGO加密解密封装
│   │   ├── encryptor_mock.go  # 加密解密模拟实现
│   │   ├── fpe.go             # 格式保留加密实现
│   │   ├── fpe_test.go        # 加密解密测试
│   │   ├── libencryptor.h     # C语言头文件
│   │   └── README.md          # 加密模块说明
│   ├── daemon/                # 守护进程实现
│   │   ├── daemon.go          # 服务进程实现
│   │   └── process.go         # 进程管理
│   ├── service/               # 业务逻辑
│   │   ├── redirect.go        # 重定向核心服务
│   │   └── redirect_test.go   # 重定向服务测试
│   ├── transformer/           # URL转换
│   │   └── transformer.go     # URL转换实现
│   └── urlparser/             # URL解析
│       ├── parser.go          # URL解析实现
│       └── parser_test.go     # URL解析测试
├── lib/                       # 共享库目录
│   └── libencryptor.so        # 加密解密共享库
├── scripts/                   # 脚本和辅助文件
│   └── shortlinkd.service     # systemd服务文件
├── config.yaml                # 配置文件
├── go.mod                     # Go模块定义
├── go.sum                     # Go依赖校验
└── README.md                  # 项目文档
```

## 配置说明

```yaml
server:
  host: "0.0.0.0"           # 服务器绑定IP
  port: 8080                # 服务器端口

log:
  level: "info"             # 日志级别
  format: "json"            # 日志格式
  path: "logs/urlproxy.log" # 日志文件路径

redirect:
  source_domain: "tnas.share.net"  # 源域名
  target_base_url: "https://tnas.online"  # 目标基础URL
  path_template: "/e_{device_id}/share/{link_id}"  # 路径模板
  
crypto:
  device_id_length: 12        # 设备ID长度
  link_id_length: 4           # 链接ID长度
  
cache:
  enabled: true               # 是否启用缓存
  ttl_seconds: 7200           # 缓存有效期(秒)
  max_size: 100000            # 最大缓存条目数

rate:
  max_requests_per_second: 5000  # 每秒最大请求数
  burst_size: 100               # 突发请求容量
  max_concurrent_requests: 200   # 最大并发请求数
```

## 使用共享库

本项目使用CGO调用C语言共享库进行加密解密操作。请按照以下步骤配置共享库：

1. 将`libencryptor.so`文件放置在`lib/`目录下
2. 确保`lib/libencryptor.h`头文件与共享库匹配
3. 编译时CGO会自动链接共享库

### 共享库接口

共享库需要提供以下C语言接口：

```c
// 加密函数
int encrypt(const char *plaintext, char *ciphertext);

// 解密函数
int decrypt(const char *ciphertext, char *plaintext);
```

两个函数都应该返回0表示成功，非0表示失败。

## 使用方法

### 基本使用

```bash
# 启动服务
./shortlinkd -config config.yaml start

# 停止服务
./shortlinkd stop

# 查看状态
./shortlinkd status

# 调试模式运行
./shortlinkd -config config.yaml debug
```

### 测试重定向

```bash
# 测试重定向（需要设置Host头）
curl -i -H "Host: tnas.share.net" http://localhost:8080/s/60f7098dc11f418d8f

# 跟随重定向
curl -L -H "Host: tnas.share.net" http://localhost:8080/s/8urfv069yadokslml5
```

## 部署

### 系统服务

1. 复制systemd服务文件
```bash
sudo cp scripts/shortlinkd.service /etc/systemd/system/
```

2. 重新加载systemd配置
```bash
sudo systemctl daemon-reload
```

3. 启用并启动服务
```bash
sudo systemctl enable shortlinkd
sudo systemctl start shortlinkd
```

## 依赖

- Go 1.23+
- C/C++编译环境（用于CGO）
- libencryptor.so（加密解密共享库）
- github.com/sevlyar/go-daemon - 守护进程支持
- golang.org/x/time - 速率限制
- gopkg.in/yaml.v3 - YAML配置解析 