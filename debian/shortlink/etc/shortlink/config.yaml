server:
  host: "0.0.0.0"
  port: 8080

# log:
#   level: "info"
#   format: "json"
#   path: "logs/urlproxy.log"

redirect:
  source_domain: "tnas.share.net"
  target_base_url: "https://tnas.online"
  path_template: "/e_{device_id}/share/{link_id}"
  
crypto:
  device_id_length: 12
  link_id_length: 4
  
cache:
  enabled: true
  ttl_seconds: 7200
  max_size: 100000

rate:
  max_requests_per_second: 5000
  burst_size: 100
  max_concurrent_requests: 200 
