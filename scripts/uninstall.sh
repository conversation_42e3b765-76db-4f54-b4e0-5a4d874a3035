#!/bin/bash
# 卸载脚本 - 卸载shortlink服务

# 确保以root权限运行
if [ "$EUID" -ne 0 ]; then
  echo "请以root权限运行此脚本"
  exit 1
fi

# 设置变量
INSTALL_DIR="/usr/bin"
CONFIG_DIR="/etc/shortlink"
LIB_DIR="/usr/lib"
INCLUDE_DIR="/usr/include"
SYSTEMD_DIR="/etc/systemd/system"

# 停止并禁用服务
echo "停止并禁用服务..."
systemctl stop shortlinkd 2>/dev/null || true
systemctl disable shortlinkd 2>/dev/null || true

# 删除systemd服务文件
echo "删除systemd服务文件..."
rm -f "$SYSTEMD_DIR/shortlinkd.service"
systemctl daemon-reload

# 删除二进制文件和配置文件
echo "删除安装的文件..."
rm -rf $INSTALL_DIR
rm -rf $CONFIG_DIR

# 删除库文件和头文件
echo "删除库文件和头文件..."
rm -f "$LIB_DIR/libencryptor.so"
rm -f "$INCLUDE_DIR/libencryptor.h"
ldconfig

echo "卸载完成！" 