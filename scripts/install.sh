#!/bin/bash
# 安装脚本 - 处理shortlink服务的部署

# 确保以root权限运行
if [ "$EUID" -ne 0 ]; then
  echo "请以root权限运行此脚本"
  exit 1
fi

# 设置变量
INSTALL_DIR="/usr/bin"
CONFIG_DIR="/etc/shortlink"
LIB_DIR="/usr/lib"
INCLUDE_DIR="/usr/include"
SYSTEMD_DIR="/etc/systemd/system"
CURRENT_DIR=$(pwd)

# 创建必要的目录
echo "创建安装目录..."
mkdir -p $INSTALL_DIR
mkdir -p $CONFIG_DIR

# 复制库文件并设置环境变量
echo "安装libencryptor库..."
cp -f "$CURRENT_DIR/lib/libencryptor.so" $LIB_DIR/
chmod 755 $LIB_DIR/libencryptor.so

# 复制头文件到系统包含目录
echo "安装头文件..."
cp -f "$CURRENT_DIR/lib/libencryptor.h" $INCLUDE_DIR/
chmod 644 $INCLUDE_DIR/libencryptor.h

# 更新动态链接器缓存
ldconfig

# 构建二进制文件
echo "构建最新的二进制程序..."
cd "$CURRENT_DIR"
export CGO_ENABLED=1
go build -o shortlinkd cmd/shortlinkd/main.go

# 复制二进制文件到安装目录
echo "安装二进制文件..."
cp -f "$CURRENT_DIR/shortlinkd" $INSTALL_DIR/
chmod 755 $INSTALL_DIR/shortlinkd

# 复制配置文件
echo "安装配置文件..."
cp -f "$CURRENT_DIR/config.yaml" $CONFIG_DIR/
chmod 644 $CONFIG_DIR/config.yaml

# 安装systemd服务
echo "安装systemd服务..."
cp -f "$CURRENT_DIR/scripts/shortlinkd.service" $SYSTEMD_DIR/
chmod 644 $SYSTEMD_DIR/shortlinkd.service

# 重新加载systemd配置
systemctl daemon-reload

echo "安装完成！"