#!/bin/bash
# 脚本用于更新和重新构建Debian包

# 设置版本号（从命令行参数获取或使用默认值）
if [ -z "$1" ]; then
    VERSION="1.0.0"
    echo "未指定版本号，使用默认版本: $VERSION"
    echo "用法: $0 <版本号>"
    echo "例如: $0 1.0.1"
else
    VERSION="$1"
fi

echo "构建版本: $VERSION"

# 重新编译二进制文件
echo "编译程序..."
go build -o shortlinkd cmd/shortlinkd/main.go

# 确保目录结构存在
if [ ! -d "debian/shortlink" ]; then
    echo "Debian包结构不存在，创建目录..."
    mkdir -p debian/shortlink/DEBIAN
    mkdir -p debian/shortlink/usr/bin
    mkdir -p debian/shortlink/usr/lib
    mkdir -p debian/shortlink/usr/include
    mkdir -p debian/shortlink/etc/shortlink
    mkdir -p debian/shortlink/lib/systemd/system
fi

# 更新文件
echo "更新文件..."
cp shortlinkd debian/shortlink/usr/bin/
cp lib/libencryptor.so debian/shortlink/usr/lib/
cp lib/libencryptor.h debian/shortlink/usr/include/
cp config.yaml debian/shortlink/etc/shortlink/
cp scripts/shortlinkd.service debian/shortlink/lib/systemd/system/

# 更新control文件中的版本
sed -i "s/^Version:.*/Version: $VERSION/" debian/shortlink/DEBIAN/control

# 如果control文件不存在，则创建
if [ ! -f debian/shortlink/DEBIAN/control ]; then
    echo "创建control文件..."
    cat > debian/shortlink/DEBIAN/control << EOF
Package: shortlink
Version: $VERSION
Section: utils
Priority: optional
Architecture: amd64
Depends: libc6
Maintainer: Your Name <<EMAIL>>
Description: URL Shortening Service
 ShortLink是一个短链接服务，能够创建简短URL重定向至长链接。
EOF
fi

# 确保安装脚本存在
if [ ! -f debian/shortlink/DEBIAN/postinst ]; then
    echo "创建postinst脚本..."
    cat > debian/shortlink/DEBIAN/postinst << EOF
#!/bin/bash
ldconfig
systemctl daemon-reload
echo "ShortLink安装完成。使用 'systemctl start shortlinkd' 启动服务。"
EOF
fi

# 确保卸载脚本存在
if [ ! -f debian/shortlink/DEBIAN/prerm ]; then
    echo "创建prerm脚本..."
    cat > debian/shortlink/DEBIAN/prerm << EOF
#!/bin/bash
systemctl stop shortlinkd || true
systemctl disable shortlinkd || true
echo "ShortLink服务已停止"
EOF
fi

# 设置权限
echo "设置权限..."
find debian/shortlink -type d -exec chmod 755 {} \;
find debian/shortlink -type f -exec chmod 644 {} \;
chmod 755 debian/shortlink/DEBIAN/postinst
chmod 755 debian/shortlink/DEBIAN/prerm
chmod 755 debian/shortlink/usr/bin/shortlinkd

# 构建Debian包
echo "构建Debian包..."
dpkg-deb --build debian/shortlink .

echo "打包完成: shortlink_${VERSION}_amd64.deb"