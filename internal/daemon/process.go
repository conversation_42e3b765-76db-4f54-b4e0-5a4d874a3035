package daemon

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"

	"github.com/sevlyar/go-daemon"
)

const (
	PipeFilePerm = 0600
)

var (
	WorkingDir = "/var/run"

	daemonizedService *Daemon

	pipFileName string
)

type asyncOperateResult struct {
	Error  string `json:"error,omitempty"`
	Output string `json:"output,omitempty"`
}

// ProcessDaemon 处理守护进程的启动、停止和状态查询
func ProcessDaemon(serviceName string, cfg interface{}, serviceGenerator func(interface{}) (*Daemon, error)) {
	const (
		ValidArguesCount = 2
		StartCommand     = "start"
		StopCommand      = "stop"
		StatusCommand    = "status"
		DebugCommand     = "debug"
	)

	if len(os.Args) != ValidArguesCount {
		printUsage(serviceName)
		return
	}

	if err := os.Mkdir<PERSON>ll(WorkingDir, 0755); err != nil {
		fmt.Printf("Failed to create working directory: %s\n", err)
		return
	}

	command := os.Args[1]
	pidFileName := filepath.Join(WorkingDir, fmt.Sprintf("%s.pid", serviceName))
	pipFileName = filepath.Join(WorkingDir, fmt.Sprintf("%s.pip", serviceName))

	daemonContext := &daemon.Context{
		PidFileName: pidFileName,
		PidFilePerm: 0644,
		LogFileName: filepath.Join(WorkingDir, fmt.Sprintf("%s.log", serviceName)),
		LogFilePerm: 0640,
		WorkDir:     "./",
		Umask:       027,
	}

	daemon.AddCommand(daemon.StringFlag(&command, StopCommand), syscall.SIGTERM, onStopDaemon)

	switch command {
	case StopCommand:
		if process, err := daemonContext.Search(); err != nil || process == nil {
			fmt.Printf("%s is already stopped\n", serviceName)
			return
		} else if !isRunning(process) {
			fmt.Printf("%s is already stopped (process %d not running)\n", serviceName, process.Pid)
		} else {
			defer os.Remove(pipFileName)
			if err = createPipe(pipFileName); err != nil {
				fmt.Printf("Failed to open pipe: %s\n", err.Error())
				return
			}
			go daemon.SendCommands(process)

			msg, err := readPipe(pipFileName)
			if err != nil {
				fmt.Printf("Failed to stop %s: %s\n", serviceName, err.Error())
			} else if msg != "" {
				fmt.Println(msg)
				fmt.Printf("Successfully stopped %s\n", serviceName)
			} else {
				fmt.Printf("Successfully stopped %s\n", serviceName)
			}
		}

	case StatusCommand:
		process, err := daemonContext.Search()
		if err != nil || process == nil {
			fmt.Printf("%s is stopped\n", serviceName)
		} else if !isRunning(process) {
			fmt.Printf("%s is stopped (pid %d)\n", serviceName, process.Pid)
		} else {
			fmt.Printf("%s is running, pid %d\n", serviceName, process.Pid)
		}

	case StartCommand:
		if process, err := daemonContext.Search(); err == nil && process != nil {
			if isRunning(process) {
				fmt.Printf("%s is already running\n", serviceName)
				return
			}
		}

		child, err := daemonContext.Reborn()
		if err != nil {
			fmt.Printf("Failed to create daemon: %s\n", err.Error())
			return
		}

		if child != nil {
			msg, err := readMessageFromPipe(pipFileName)
			if err != nil {
				fmt.Printf("Failed to start %s: %s\n", serviceName, err.Error())
			} else {
				fmt.Println(msg)
				fmt.Printf("%s started\n", serviceName)
			}
			return
		}

		defer os.Remove(pidFileName)

		daemonizedService, err = serviceGenerator(cfg)
		if err != nil {
			log.Printf("Failed to create service: %s", err.Error())
			notifyErrorToPipe(pipFileName, err.Error())
			return
		}

		err = daemonizedService.Start(context.Background())
		if err != nil {
			log.Printf("Failed to start service: %s", err.Error())
			notifyErrorToPipe(pipFileName, err.Error())
		} else {
			notifyMessageToPipe(pipFileName, "Shortlink service started")
			daemon.ServeSignals()
		}

	case DebugCommand:
		var err error
		daemonizedService, err = serviceGenerator(cfg)
		if err != nil {
			log.Printf("Failed to create service: %s", err.Error())
			return
		}

		err = daemonizedService.Start(context.Background())
		if err != nil {
			log.Printf("Failed to start service: %s", err.Error())
			return
		}

		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM)
		<-ch

		daemonizedService.Stop()

	default:
		printUsage(serviceName)
	}
}

// 停止守护进程的回调函数
func onStopDaemon(sig os.Signal) error {
	if daemonizedService == nil {
		log.Println("Invalid daemon service")
		return daemon.ErrStop
	}
	if pipFileName == "" {
		log.Println("Invalid pipe file")
		return daemon.ErrStop
	}

	err := daemonizedService.Stop()
	if err != nil {
		log.Printf("Failed to stop service: %s", err.Error())
		notifyErrorToPipe(pipFileName, err.Error())
	} else {
		notifyMessageToPipe(pipFileName, "Shortlink service stopped")
	}

	return daemon.ErrStop
}

func readMessageFromPipe(pipeName string) (message string, err error) {
	defer os.Remove(pipeName)
	if err = createPipe(pipeName); err != nil {
		return
	}
	message, err = readPipe(pipeName)
	return
}

func createPipe(pipeName string) (err error) {
	if _, err = os.Stat(pipeName); !os.IsNotExist(err) {
		os.Remove(pipeName)
	}
	if err = syscall.Mkfifo(pipeName, PipeFilePerm); err != nil {
		return
	}
	return nil
}

func readPipe(pipeName string) (message string, err error) {
	pipe, err := os.OpenFile(pipeName, os.O_RDONLY, PipeFilePerm)
	if err != nil {
		return
	}
	defer pipe.Close()

	data := make([]byte, 1<<10)
	n, err := pipe.Read(data)
	if err != nil {
		return
	}

	var result asyncOperateResult
	if err = json.Unmarshal(data[:n], &result); err != nil {
		return "", fmt.Errorf("parse error: %s, data %s", err.Error(), data[:n])
	}

	if result.Error != "" {
		err = errors.New(result.Error)
	} else {
		message = result.Output
	}
	return
}

func notifyMessageToPipe(pipeName, message string) (err error) {
	pip, err := os.OpenFile(pipeName, os.O_RDWR, PipeFilePerm)
	if err != nil {
		return
	}
	defer pip.Close()

	result := asyncOperateResult{Output: message}
	data, err := json.MarshalIndent(result, "", " ")
	if err != nil {
		return
	}
	_, err = pip.Write(data)
	return
}

func notifyErrorToPipe(pipeName, message string) (err error) {
	pip, err := os.OpenFile(pipeName, os.O_RDWR, PipeFilePerm)
	if err != nil {
		return
	}
	defer pip.Close()

	result := asyncOperateResult{Error: message}
	data, err := json.MarshalIndent(result, "", " ")
	if err != nil {
		return
	}
	_, err = pip.Write(data)
	return
}

// 检查进程是否运行
func isRunning(process *os.Process) bool {
	if process == nil {
		return false
	}
	if err := process.Signal(syscall.Signal(0)); err == nil {
		return true
	}
	return false
}

// 打印使用说明
func printUsage(serviceName string) {
	fmt.Printf("Usage: %s [start|stop|status|debug]\n", serviceName)
}
