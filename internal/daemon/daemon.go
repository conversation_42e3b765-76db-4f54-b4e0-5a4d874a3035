package daemon

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/test/shortlink/internal/config"
	"github.com/test/shortlink/internal/service"
)

type Daemon struct {
	cfg             *config.Config
	server          *http.Server
	redirectService *service.RedirectService
	shutdownCh      chan struct{}
}

func New(cfg *config.Config) (*Daemon, error) {
	redirectSvc, err := service.NewRedirectService(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create redirect service: %w", err)
	}

	return &Daemon{
		cfg:             cfg,
		redirectService: redirectSvc,
		shutdownCh:      make(chan struct{}),
	}, nil
}

func (d *Daemon) Start(ctx context.Context) error {
	mux := http.NewServeMux()

	// 注册重定向处理器
	mux.HandleFunc("/s/", d.redirectService.HandleRedirect)

	// 健康检查端点
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	// 服务统计信息端点
	mux.HandleFunc("/stats", func(w http.ResponseWriter, r *http.Request) {
		stats := d.redirectService.GetStats()
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"total_requests":%d,"cache_hits":%d,"cache_misses":%d,"errors":%d,"rate_limited":%d}`,
			stats.TotalRequests, stats.CacheHits, stats.CacheMisses, stats.ProcessingErrors, stats.RateLimitExceeded)
	})

	addr := d.cfg.Server.Host + ":" + strconv.Itoa(d.cfg.Server.Port)
	d.server = &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	go func() {
		log.Printf("Starting server on %s", addr)
		if err := d.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server error: %v", err)
		}
	}()

	if ctx != nil {
		go func() {
			<-ctx.Done()
			d.Stop()
		}()
	}

	return nil
}

func (d *Daemon) Stop() error {
	if d.server != nil {
		log.Println("Shutting down HTTP server...")

		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := d.server.Shutdown(ctx); err != nil {
			return fmt.Errorf("server shutdown failed: %w", err)
		}
	}

	// 停止缓存清理协程
	if d.redirectService != nil {
		log.Println("Stopping cache cleanup...")
		d.redirectService.StopCache()
	}

	select {
	case <-d.shutdownCh:
	default:
		close(d.shutdownCh)
	}
	return nil
}
