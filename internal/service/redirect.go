package service

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"golang.org/x/time/rate"

	"github.com/test/shortlink/internal/cache"
	"github.com/test/shortlink/internal/config"
	"github.com/test/shortlink/internal/crypto"
	"github.com/test/shortlink/internal/transformer"
	"github.com/test/shortlink/internal/urlparser"
)

type RedirectService struct {
	urlParser      *urlparser.URLParser
	fpeDecryptor   *crypto.FPECrypto
	urlTransformer *transformer.URLTransformer
	cache          *cache.Cache
	limiter        *rate.Limiter
	semaphore      chan struct{}
	stats          *Stats
	config         *config.Config
}

type Stats struct {
	TotalRequests      int64
	CacheHits          int64
	CacheMisses        int64
	ProcessingErrors   int64
	RateLimitExceeded  int64
	LastRequestTime    time.Time
	AverageProcessTime time.Duration
	totalProcessTime   time.Duration
	mutex              sync.RWMutex
}

func NewRedirectService(cfg *config.Config) (*RedirectService, error) {
	parser := urlparser.New(cfg)

	decryptor, err := crypto.New(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize crypto: %w", err)
	}

	transformer := transformer.New(cfg)
	cache := cache.New(cfg)

	limiter := rate.NewLimiter(rate.Limit(cfg.Rate.MaxRequestsPerSecond), cfg.Rate.BurstSize)

	semaphore := make(chan struct{}, cfg.Rate.MaxConcurrentRequests)

	return &RedirectService{
		urlParser:      parser,
		fpeDecryptor:   decryptor,
		urlTransformer: transformer,
		cache:          cache,
		limiter:        limiter,
		semaphore:      semaphore,
		stats: &Stats{
			mutex: sync.RWMutex{},
		},
		config: cfg,
	}, nil
}

func (s *RedirectService) HandleRedirect(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	s.stats.mutex.Lock()
	s.stats.TotalRequests++
	s.stats.LastRequestTime = startTime
	s.stats.mutex.Unlock()

	if !s.limiter.Allow() {
		s.stats.mutex.Lock()
		s.stats.RateLimitExceeded++
		s.stats.mutex.Unlock()

		http.Error(w, "Request frequency too high, please try again later", http.StatusTooManyRequests)
		return
	}

	select {
	case s.semaphore <- struct{}{}:
		defer func() { <-s.semaphore }()
	default:
		http.Error(w, "Server busy, please try again later", http.StatusServiceUnavailable)
		return
	}

	path := r.URL.Path

	if !strings.HasPrefix(path, "/s/") {
		http.Error(w, "Invalid path", http.StatusBadRequest)
		return
	}

	if err := s.urlParser.ValidateDomain(r.Host); err != nil {
		http.Error(w, "Invalid request source", http.StatusForbidden)
		return
	}

	if targetURL, found := s.cache.Get(path); found {
		s.stats.mutex.Lock()
		s.stats.CacheHits++
		s.stats.mutex.Unlock()

		log.Printf("Cache hit: %s -> %s", path, targetURL)
		http.Redirect(w, r, targetURL, http.StatusTemporaryRedirect)
		return
	}

	s.stats.mutex.Lock()
	s.stats.CacheMisses++
	s.stats.mutex.Unlock()

	ctx, cancel := context.WithTimeout(r.Context(), 2*time.Second)
	defer cancel()

	targetURL, err := s.ProcessURLWithTimeout(ctx, path)
	if err != nil {
		s.stats.mutex.Lock()
		s.stats.ProcessingErrors++
		s.stats.mutex.Unlock()

		log.Printf("Error processing URL %s: %v", path, err)
		http.Error(w, "Invalid URL", http.StatusBadRequest)
		return
	}

	s.cache.Set(path, targetURL)

	processingTime := time.Since(startTime)
	s.stats.mutex.Lock()
	s.stats.totalProcessTime += processingTime
	s.stats.AverageProcessTime = s.stats.totalProcessTime / time.Duration(s.stats.TotalRequests)
	s.stats.mutex.Unlock()

	// 执行重定向
	log.Printf("Redirect %s -> %s (Processing time: %v)", path, targetURL, processingTime)
	http.Redirect(w, r, targetURL, http.StatusTemporaryRedirect)
}

func (s *RedirectService) ProcessURLWithTimeout(ctx context.Context, path string) (string, error) {
	resultCh := make(chan string, 1)
	errCh := make(chan error, 1)

	go func() {
		result, err := s.ProcessURL(path)
		if err != nil {
			errCh <- err
			return
		}
		resultCh <- result
	}()

	select {
	case <-ctx.Done():
		return "", fmt.Errorf("processing timeout")
	case err := <-errCh:
		return "", err
	case result := <-resultCh:
		return result, nil
	}
}

func (s *RedirectService) ProcessURL(path string) (string, error) {
	const (
		maxRetries    = 3
		retryInterval = 50 * time.Millisecond
	)

	var (
		shortCode     string
		decryptedData string
		err           error
		retryCount    = 0
	)

	shortCode, err = s.urlParser.Parse(path)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	for retryCount <= maxRetries {
		decryptedData, err = s.fpeDecryptor.DecryptURL(shortCode)
		if err == nil {
			break
		}

		if strings.Contains(err.Error(), "failed to decrypt") {
			if retryCount < maxRetries {
				retryCount++
				log.Printf("Retry %d/%d decrypting URL: %s", retryCount, maxRetries, shortCode)
				time.Sleep(retryInterval * time.Duration(retryCount))
				continue
			}
		}

		return "", fmt.Errorf("failed to decrypt URL: %w", err)
	}

	deviceIDLength := s.config.Crypto.DeviceIDLength
	linkIDLength := s.config.Crypto.LinkIDLength

	if len(decryptedData) < deviceIDLength+linkIDLength {
		return "", fmt.Errorf("decrypted data length is less than expected: need at least %d characters, got %d",
			deviceIDLength+linkIDLength, len(decryptedData))
	}

	deviceID := decryptedData[:deviceIDLength]
	linkID := decryptedData[deviceIDLength : deviceIDLength+linkIDLength]

	var reencryptedDeviceID string
	retryCount = 0

	for retryCount <= maxRetries {
		reencryptedDeviceID, err = s.fpeDecryptor.EncryptDeviceID(deviceID)
		if err == nil {
			break
		}

		if strings.Contains(err.Error(), "failed to encrypt") {
			if retryCount < maxRetries {
				retryCount++
				log.Printf("Retry %d/%d encrypting device ID", retryCount, maxRetries)
				time.Sleep(retryInterval * time.Duration(retryCount))
				continue
			}
		}

		return "", fmt.Errorf("failed to re-encrypt device ID: %w", err)
	}

	targetURL := s.urlTransformer.Transform(reencryptedDeviceID, linkID)

	return targetURL, nil
}

func (s *RedirectService) GetStats() Stats {
	s.stats.mutex.RLock()
	defer s.stats.mutex.RUnlock()

	return Stats{
		TotalRequests:      s.stats.TotalRequests,
		CacheHits:          s.stats.CacheHits,
		CacheMisses:        s.stats.CacheMisses,
		ProcessingErrors:   s.stats.ProcessingErrors,
		RateLimitExceeded:  s.stats.RateLimitExceeded,
		LastRequestTime:    s.stats.LastRequestTime,
		AverageProcessTime: s.stats.AverageProcessTime,
		totalProcessTime:   s.stats.totalProcessTime,
	}
}

func (s *RedirectService) StopCache() {
	if s.cache != nil {
		s.cache.Stop()
	}
}
