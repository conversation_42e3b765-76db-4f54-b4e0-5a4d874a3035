package service

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/test/shortlink/internal/config"
)

// TestRedirectIntegration 测试完整的重定向流程
func TestRedirectIntegration(t *testing.T) {
	// 加载测试配置
	cfg := &config.Config{
		Crypto: config.CryptoConfig{
			DeviceIDLength: 12,
			LinkIDLength:   4,
		},
		Redirect: config.RedirectConfig{
			SourceDomain:  "tnas.share.net",
			TargetBaseURL: "https://tnas.online",
			PathTemplate:  "/{device_id}/share/{link_id}",
		},
		Cache: config.CacheConfig{
			Enabled:    true,
			TTLSeconds: 60,
			MaxSize:    100,
		},
		Rate: config.RateConfig{
			MaxRequestsPerSecond:  100,
			BurstSize:             10,
			MaxConcurrentRequests: 10,
		},
	}

	// 创建重定向服务
	svc, err := NewRedirectService(cfg)
	if err != nil {
		t.Fatalf("Failed to create redirect service: %v", err)
	}

	// 创建测试HTTP服务器
	server := httptest.NewServer(http.HandlerFunc(svc.HandleRedirect))
	defer server.Close()

	// 设置不跟随重定向的HTTP客户端
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}

	// 测试用例
	tests := []struct {
		name           string
		path           string
		expectedStatus int
		checkLocation  bool
	}{
		{
			name:           "Valid redirect path",
			path:           "/s/8urfv069yadokslml5",
			expectedStatus: http.StatusTemporaryRedirect,
			checkLocation:  true,
		},
		{
			name:           "Invalid path without prefix",
			path:           "/invalid/path",
			expectedStatus: http.StatusBadRequest,
			checkLocation:  false,
		},
		{
			name:           "Invalid path with empty short code",
			path:           "/s/",
			expectedStatus: http.StatusBadRequest,
			checkLocation:  false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// 构造请求
			req, err := http.NewRequest("GET", server.URL+tc.path, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}

			// 设置Host头以通过域名验证
			req.Host = "tnas.share.net"

			// 发送请求
			resp, err := client.Do(req)
			if err != nil {
				t.Fatalf("Request failed: %v", err)
			}
			defer resp.Body.Close()

			// 检查状态码
			if resp.StatusCode != tc.expectedStatus {
				t.Errorf("Expected status %d, got %d", tc.expectedStatus, resp.StatusCode)
			}

			// 如果是重定向，检查Location头
			if tc.checkLocation {
				location := resp.Header.Get("Location")
				if location == "" {
					t.Error("Expected Location header, but got none")
				}
			}
		})
	}

	// 测试缓存功能
	t.Run("Cache functionality", func(t *testing.T) {
		path := "/s/60f7098dc11f418d8f"
		req, _ := http.NewRequest("GET", server.URL+path, nil)
		req.Host = "tnas.share.net"
		resp, err := client.Do(req)
		if err != nil {
			t.Fatalf("First request failed: %v", err)
		}
		resp.Body.Close()

		initialStats := svc.GetStats()
		if initialStats.CacheHits != 0 {
			t.Errorf("Expected 0 cache hits on first request, got %d", initialStats.CacheHits)
		}

		req, _ = http.NewRequest("GET", server.URL+path, nil)
		req.Host = "tnas.share.net"
		resp, err = client.Do(req)
		if err != nil {
			t.Fatalf("Second request failed: %v", err)
		}
		resp.Body.Close()

		updatedStats := svc.GetStats()
		if updatedStats.CacheHits != 1 {
			t.Errorf("Expected 1 cache hit on second request, got %d", updatedStats.CacheHits)
		}
	})
}
