package transformer

import (
	"strings"

	"github.com/test/shortlink/internal/config"
)

type URLTransformer struct {
	baseURL      string
	pathTemplate string
}

func New(cfg *config.Config) *URLTransformer {
	return &URLTransformer{
		baseURL:      cfg.Redirect.TargetBaseURL,
		pathTemplate: cfg.Redirect.PathTemplate,
	}
}

func (t *URLTransformer) Transform(deviceID, linkID string) string {
	path := strings.ReplaceAll(t.pathTemplate, "{device_id}", deviceID)
	path = strings.ReplaceAll(path, "{link_id}", linkID)

	if strings.HasSuffix(t.baseURL, "/") && strings.HasPrefix(path, "/") {
		return t.baseURL + path[1:]
	}

	if !strings.HasSuffix(t.baseURL, "/") && !strings.HasPrefix(path, "/") {
		return t.baseURL + "/" + path
	}

	return t.baseURL + path
}
