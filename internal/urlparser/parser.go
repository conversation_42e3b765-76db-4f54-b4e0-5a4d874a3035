package urlparser

import (
	"errors"
	"fmt"
	"strings"

	"github.com/test/shortlink/internal/config"
)

type URLParser struct {
	sourceDomain string
}

func New(cfg *config.Config) *URLParser {
	return &URLParser{
		sourceDomain: cfg.Redirect.SourceDomain,
	}
}

func (p *URLParser) Parse(path string) (string, error) {
	path = strings.TrimPrefix(path, "/s/")

	if path == "" {
		return "", fmt.Errorf("invalid URL format: path is empty")
	}

	return path, nil
}

func (p *URLParser) ValidateDomain(host string) error {
	if p.sourceDomain == "" {
		return nil
	}

	if !strings.HasSuffix(host, p.sourceDomain) {
		return errors.New("invalid source domain")
	}

	return nil
}

func (p *URLParser) ExtractPathFromURL(fullURL string) (string, error) {
	parts := strings.Split(fullURL, "/s/")
	if len(parts) != 2 {
		return "", errors.New("invalid URL format: missing '/s/' path")
	}

	return parts[1], nil
}
