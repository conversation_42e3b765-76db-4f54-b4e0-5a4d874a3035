package urlparser

import (
	"testing"
)

func TestParse(t *testing.T) {
	parser := &URLParser{}

	tests := []struct {
		name        string
		path        string
		expected    string
		expectError bool
	}{
		{
			name:        "Valid path with prefix",
			path:        "/s/60f7098dc11f418d8f",
			expected:    "60f7098dc11f418d8f",
			expectError: false,
		},
		{
			name:        "Valid path without prefix",
			path:        "60f7098dc11f418d8f",
			expected:    "60f7098dc11f418d8f",
			expectError: false,
		},
		{
			name:        "Empty path with prefix",
			path:        "/s/",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Empty path without prefix",
			path:        "",
			expected:    "",
			expectError: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result, err := parser.Parse(tc.path)

			if tc.expectError && err == nil {
				t.<PERSON>("Expected error but got none")
			}

			if !tc.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if result != tc.expected {
				t.<PERSON><PERSON>rf("Expected result %q but got %q", tc.expected, result)
			}
		})
	}
}

func TestValidateDomain(t *testing.T) {
	tests := []struct {
		name         string
		sourceDomain string
		host         string
		expectError  bool
	}{
		{
			name:         "Valid domain",
			sourceDomain: "tnas.share.net",
			host:         "tnas.share.net",
			expectError:  false,
		},
		{
			name:         "Valid subdomain",
			sourceDomain: "tnas.share.net",
			host:         "sub.tnas.share.net",
			expectError:  false,
		},
		{
			name:         "Invalid domain",
			sourceDomain: "tnas.share.net",
			host:         "other-domain.com",
			expectError:  true,
		},
		{
			name:         "Empty domain config - should always pass",
			sourceDomain: "",
			host:         "any-domain.com",
			expectError:  false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			parser := &URLParser{
				sourceDomain: tc.sourceDomain,
			}

			err := parser.ValidateDomain(tc.host)

			if tc.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}

			if !tc.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})
	}
}
