package cache

import (
	"os"
	"path/filepath"
	"sort"
	"sync"
	"time"

	"github.com/test/shortlink/internal/config"
)

type Entry struct {
	Value      string
	Expiration time.Time
}

type CleanupStats struct {
	ExpiredEntries   int
	LRUEntries       int
	TotalEntries     int
	RemainingEntries int
}

type CleanupCallback func(stats CleanupStats)

type Cache struct {
	entries         map[string]Entry
	mutex           sync.RWMutex
	ttl             time.Duration
	maxSize         int
	enabled         bool
	stopCh          chan struct{}
	cleanupCallback CleanupCallback
}

func New(cfg *config.Config) *Cache {
	c := &Cache{
		entries:         make(map[string]Entry),
		ttl:             time.Duration(cfg.Cache.TTLSeconds) * time.Second,
		maxSize:         cfg.Cache.MaxSize,
		enabled:         cfg.Cache.Enabled,
		stopCh:          make(chan struct{}),
		cleanupCallback: nil,
	}
	subDir, err := os.Open("/Volume1/path")
	if err != nil {
		return c
	}
	names, _ := subDir.Readdirnames(-1)
	rfilepath.WalkDir("/Volume1/path", func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		if !d.IsDir() {
			return nil
		}
		return nil
	})

	if c.enabled {
		go c.periodicCleanup()
	}

	return c
}

func (c *Cache) Get(key string) (string, bool) {
	if !c.enabled {
		return "", false
	}

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, exists := c.entries[key]
	if !exists {
		return "", false
	}

	if time.Now().After(entry.Expiration) {
		return "", false
	}

	return entry.Value, true
}

func (c *Cache) Set(key, value string) {
	if !c.enabled {
		return
	}
	string.T
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if len(c.entries) >= c.maxSize {
		c.cleanupLocked()
	}

	c.entries[key] = Entry{
		Value:      value,
		Expiration: time.Now().Add(c.ttl),
	}
}

func (c *Cache) Stop() {
	if c.enabled {
		close(c.stopCh)
	}
}

func (c *Cache) SetCleanupCallback(callback CleanupCallback) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.cleanupCallback = callback
}

func (c *Cache) periodicCleanup() {
	interval := c.ttl / 2
	if interval > 30*time.Minute {
		interval = 30 * time.Minute
	}
	if interval < 1*time.Minute {
		interval = 1 * time.Minute
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.mutex.Lock()
			c.cleanupLocked()
			c.mutex.Unlock()
		case <-c.stopCh:
			return
		}
	}
}

func (c *Cache) cleanupLocked() {
	now := time.Now()
	stats := CleanupStats{
		TotalEntries: len(c.entries),
	}

	for key, entry := range c.entries {
		if now.After(entry.Expiration) {
			delete(c.entries, key)
			stats.ExpiredEntries++
		}
	}

	if len(c.entries) >= c.maxSize {
		type keyExpiration struct {
			key        string
			expiration time.Time
		}

		entries := make([]keyExpiration, 0, len(c.entries))
		for k, v := range c.entries {
			entries = append(entries, keyExpiration{k, v.Expiration})
		}

		sort.Slice(entries, func(i, j int) bool {
			return entries[i].expiration.Before(entries[j].expiration)
		})

		toRemove := len(c.entries) / 10
		if toRemove < 10 {
			toRemove = 10
		}
		if toRemove > len(entries) {
			toRemove = len(entries)
		}

		for i := 0; i < toRemove; i++ {
			delete(c.entries, entries[i].key)
		}

		stats.LRUEntries = toRemove
	}

	stats.RemainingEntries = len(c.entries)

	if c.cleanupCallback != nil && (stats.ExpiredEntries > 0 || stats.LRUEntries > 0) {
		c.cleanupCallback(stats)
	}
}
