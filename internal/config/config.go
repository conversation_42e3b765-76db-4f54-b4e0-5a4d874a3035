package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server ServerConfig `yaml:"server"`
	// Log      LogConfig      `yaml:"log"`
	Redirect RedirectConfig `yaml:"redirect"`
	Crypto   CryptoConfig   `yaml:"crypto"`
	Cache    CacheConfig    `yaml:"cache"`
	Rate     RateConfig     `yaml:"rate"`
}

type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// type LogConfig struct {
// 	Level  string `yaml:"level"`
// 	Format string `yaml:"format"`
// 	Path   string `yaml:"path"`
// }

type RedirectConfig struct {
	SourceDomain  string `yaml:"source_domain"`
	TargetBaseURL string `yaml:"target_base_url"`
	PathTemplate  string `yaml:"path_template"`
}

type CryptoConfig struct {
	DeviceIDLength int `yaml:"device_id_length"`
	LinkIDLength   int `yaml:"link_id_length"`
}

type CacheConfig struct {
	Enabled    bool `yaml:"enabled"`
	TTLSeconds int  `yaml:"ttl_seconds"`
	MaxSize    int  `yaml:"max_size"`
}

type RateConfig struct {
	MaxRequestsPerSecond  int `yaml:"max_requests_per_second"`
	BurstSize             int `yaml:"burst_size"`
	MaxConcurrentRequests int `yaml:"max_concurrent_requests"`
}

func Load(path string) (*Config, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var cfg Config
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return &cfg, nil
}
