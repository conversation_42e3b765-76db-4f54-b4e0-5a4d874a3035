package crypto

import (
	"fmt"

	"github.com/test/shortlink/internal/config"
)

type FPECrypto struct {
	deviceIDLength int
	linkIDLength   int
}

func New(cfg *config.Config) (*FPECrypto, error) {
	return &FPECrypto{
		deviceIDLength: cfg.Crypto.DeviceIDLength,
		linkIDLength:   cfg.Crypto.LinkIDLength,
	}, nil
}

func (f *FPECrypto) DecryptURL(plainURL string) (string, error) {
	decryptedURL, err := f.DecryptLinkID(plainURL)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt URL: %w", err)
	}
	return decryptedURL, nil
}

func (f *FPECrypto) DecryptDeviceID(encryptedID string) (string, error) {
	decryptedID, err := Decrypt(encryptedID)
	if err != nil {
		return "", fmt.<PERSON>rrorf("failed to decrypt device ID: %w", err)
	}
	return decryptedID, nil
}

func (f *FPECrypto) DecryptLinkID(encryptedID string) (string, error) {
	decryptedID, err := Decrypt(encryptedID)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt link ID: %w", err)
	}
	return decryptedID, nil
}

func (f *FPECrypto) EncryptDeviceID(plainID string) (string, error) {
	encryptedID, err := Encrypt(plainID)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt device ID: %w", err)
	}
	return encryptedID, nil
}

func (f *FPECrypto) EncryptLinkID(plainID string) (string, error) {
	encryptedID, err := Encrypt(plainID)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt link ID: %w", err)
	}
	return encryptedID, nil
}
