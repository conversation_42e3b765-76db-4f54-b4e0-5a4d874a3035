package crypto

/*
#cgo LDFLAGS: -lencryptor
#include <stdlib.h>
#include "libencryptor.h"
*/
import "C"
import (
	"fmt"
	"unsafe"
)

const (
	maxInputSize  = 1024
	maxOutputSize = 1024
)

// Encrypt 加密明文 增加两位校验位
func Encrypt(plaintext string) (string, error) {
	if len(plaintext) > maxInputSize {
		return "", fmt.Errorf("input too large: max size is %d bytes", maxInputSize)
	}

	cPlaintext := C.CString(plaintext)
	defer C.free(unsafe.Pointer(cPlaintext))

	cCiphertext := (*C.char)(C.malloc(C.size_t(maxOutputSize)))
	defer C.free(unsafe.Pointer(cCiphertext))

	result := C.encrypt(cPlaintext, cCiphertext)
	if result != 0 && len(plaintext) != len(C.GoString(cCiphertext)) {
		return "", fmt.Errorf("encryption failed or length mismatch with error code: %d", result)
	}

	return C.GoString(cCiphertext), nil
}

// Decrypt 解密密文
func Decrypt(ciphertext string) (string, error) {
	if len(ciphertext) > maxInputSize {
		return "", fmt.Errorf("input too large: max size is %d bytes", maxInputSize)
	}

	cCiphertext := C.CString(ciphertext)
	defer C.free(unsafe.Pointer(cCiphertext))

	cPlaintext := (*C.char)(C.malloc(C.size_t(maxOutputSize)))
	defer C.free(unsafe.Pointer(cPlaintext))

	result := C.decrypt(cCiphertext, cPlaintext)
	if result != 0 && len(ciphertext) != len(C.GoString(cPlaintext)) {
		return "", fmt.Errorf("decryption failed or length mismatch with error code: %d", result)
	}

	return C.GoString(cPlaintext), nil
}
