package crypto

import (
	"testing"

	"github.com/test/shortlink/internal/config"
)

func getTestConfig() *config.Config {
	return &config.Config{
		Crypto: config.CryptoConfig{
			DeviceIDLength: 12,
			LinkIDLength:   4,
		},
	}
}

// TestDirectEncryptDecrypt 直接测试加密解密函数
// 加密会增加两位校验位, 解密会对传来的字符串后两位做校验,
// 所有长度需要符合要求, 对url解密后长度正常为16位 = 12位设备id + 4位分享文件id
// 重新组合重定向url时, 设备id重新加密12位->14位
func TestDirectEncryptDecrypt(t *testing.T) {
	testCases := []struct {
		name      string
		plaintext string
	}{
		{
			name:      "Normal String",
			plaintext: "teststring123",
		},
		{
			name:      "Device ID Format",
			plaintext: "123456789012",
		},
		{
			name:      "Link ID Format",
			plaintext: "qdt8",
		},
		{
			name:      "URL Format",
			plaintext: "1234567890123456",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Encrypt
			ciphertext, err := Encrypt(tc.plaintext)
			if err != nil {
				t.Fatalf("Encryption failed: %v", err)
			}
			t.Logf("Encryption result: %s -> %s", tc.plaintext, ciphertext)

			// Decrypt
			decrypted, err := Decrypt(ciphertext)
			if err != nil {
				t.Fatalf("Decryption failed: %v", err)
			}
			t.Logf("Decryption result: %s -> %s", ciphertext, decrypted)

			// Verify result
			if decrypted != tc.plaintext {
				t.Errorf("Decryption result doesn't match original, expected: %s, actual: %s", tc.plaintext, decrypted)
			}
		})
	}
}

// TestFPECryptoEncryptDecrypt 测试FPECrypto的加密解密功能
func TestFPECryptoEncryptDecrypt(t *testing.T) {
	cfg := getTestConfig()

	crypto, err := New(cfg)
	if err != nil {
		t.Fatalf("Failed to create FPECrypto: %v", err)
	}

	testCases := []struct {
		name     string
		deviceID string
		linkID   string
	}{
		{
			name:     "Standard Format",
			deviceID: "123456789012",
			linkID:   "qdt8",
		},
		{
			name:     "Numeric Format",
			deviceID: "000000000000",
			linkID:   "0000",
		},
		{
			name:     "Mixed Format",
			deviceID: "abcdef123456",
			linkID:   "abc1",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			encryptedDeviceID, err := crypto.EncryptDeviceID(tc.deviceID)
			if err != nil {
				t.Fatalf("Device ID encryption failed: %v", err)
			}
			t.Logf("Device ID encryption result: %s -> %s", tc.deviceID, encryptedDeviceID)

			decryptedDeviceID, err := crypto.DecryptDeviceID(encryptedDeviceID)
			if err != nil {
				t.Fatalf("Device ID decryption failed: %v", err)
			}
			t.Logf("Device ID decryption result: %s -> %s", encryptedDeviceID, decryptedDeviceID)

			if decryptedDeviceID != tc.deviceID {
				t.Errorf("Device ID decryption result doesn't match original, expected: %s, actual: %s", tc.deviceID, decryptedDeviceID)
			}

			// Test link ID encryption/decryption
			encryptedLinkID, err := crypto.EncryptLinkID(tc.linkID)
			if err != nil {
				t.Fatalf("Link ID encryption failed: %v", err)
			}
			t.Logf("Link ID encryption result: %s -> %s", tc.linkID, encryptedLinkID)

			decryptedLinkID, err := crypto.DecryptLinkID(encryptedLinkID)
			if err != nil {
				t.Fatalf("Link ID decryption failed: %v", err)
			}
			t.Logf("Link ID decryption result: %s -> %s", encryptedLinkID, decryptedLinkID)

			if decryptedLinkID != tc.linkID {
				t.Errorf("Link ID decryption result doesn't match original, expected: %s, actual: %s", tc.linkID, decryptedLinkID)
			}
		})
	}
}
